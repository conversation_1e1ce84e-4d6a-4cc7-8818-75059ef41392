name: 🚀 Deploy to AWS Staging

on:
  push:
    branches: [ main, master, dev ]
  pull_request:
    branches: [ main, master ]
  workflow_dispatch:
    inputs:
      force_deploy:
        description: 'Force deployment even if CI is not green'
        required: false
        default: false
        type: boolean
      skip_tests:
        description: 'Skip pre-deployment tests'
        required: false
        default: false
        type: boolean

env:
  AWS_DEFAULT_REGION: 'us-east-1'
  ENVIRONMENT: 'staging'
  DOCKER_REGISTRY: 'ghcr.io'
  IMAGE_NAME: 'fluxtrader'
  EC2_INSTANCE_TYPE: 't3.medium'
  RDS_INSTANCE_CLASS: 'db.t3.micro'

jobs:
  # Job 1: Pre-deployment validation
  pre-deployment:
    name: 🔍 Pre-deployment Validation
    runs-on: ubuntu-latest
    timeout-minutes: 15
    outputs:
      should_deploy: ${{ steps.check.outputs.should_deploy }}
      version: ${{ steps.version.outputs.version }}
      commit_sha: ${{ steps.version.outputs.commit_sha }}
    
    steps:
    - name: 📥 Checkout code
      uses: actions/checkout@v4
      with:
        fetch-depth: 0
        
    - name: 🔧 Configure AWS credentials
      uses: aws-actions/configure-aws-credentials@v4
      with:
        aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
        aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
        aws-region: ${{ env.AWS_DEFAULT_REGION }}
        
    - name: 🔍 Check CI status and deployment readiness
      id: check
      run: |
        if [[ "${{ github.event.inputs.force_deploy }}" == "true" ]]; then
          echo "🚨 Force deployment requested"
          echo "should_deploy=true" >> $GITHUB_OUTPUT
        else
          echo "🔍 Checking CI status and AWS connectivity..."
          
          # Test AWS connectivity
          aws sts get-caller-identity
          
          # Check if secrets exist
          aws secretsmanager describe-secret --secret-id "fluxtrader/staging/database/main" || echo "⚠️ Database secrets not found"
          aws secretsmanager describe-secret --secret-id "fluxtrader/staging/trading/api-keys" || echo "⚠️ Trading API secrets not found"
          
          echo "should_deploy=true" >> $GITHUB_OUTPUT
        fi
        
    - name: 📋 Generate version and metadata
      id: version
      run: |
        COMMIT_SHA=$(git rev-parse --short HEAD)
        TIMESTAMP=$(date +%Y%m%d-%H%M%S)
        VERSION="v${TIMESTAMP}-${COMMIT_SHA}"
        
        echo "version=$VERSION" >> $GITHUB_OUTPUT
        echo "commit_sha=$COMMIT_SHA" >> $GITHUB_OUTPUT
        echo "📋 Version: $VERSION"
        echo "📋 Commit: $COMMIT_SHA"
        
    - name: 🧪 Pre-deployment tests
      if: github.event.inputs.skip_tests != 'true'
      run: |
        echo "🧪 Running pre-deployment validation tests..."
        
        # Test AWS Secrets Manager connectivity
        python3 -c "
        import boto3
        import json
        
        try:
            client = boto3.client('secretsmanager', region_name='${{ env.AWS_DEFAULT_REGION }}')
            
            # Test connectivity
            response = client.list_secrets(MaxResults=1)
            print('✅ AWS Secrets Manager connectivity verified')
            
        except Exception as e:
            print(f'⚠️ AWS Secrets Manager test failed: {e}')
        "

  # Job 2: Build and push Docker image
  build-and-push:
    name: 🏗️ Build & Push to Registry
    runs-on: ubuntu-latest
    timeout-minutes: 30
    needs: pre-deployment
    if: needs.pre-deployment.outputs.should_deploy == 'true'
    outputs:
      image_tag: ${{ steps.meta.outputs.tags }}
      image_digest: ${{ steps.build.outputs.digest }}
    
    steps:
    - name: 📥 Checkout code
      uses: actions/checkout@v4
      
    - name: 🔧 Configure AWS credentials
      uses: aws-actions/configure-aws-credentials@v4
      with:
        aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
        aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
        aws-region: ${{ env.AWS_DEFAULT_REGION }}
      
    - name: 🐳 Set up Docker Buildx
      uses: docker/setup-buildx-action@v3
      
    - name: 🔐 Log in to Container Registry
      uses: docker/login-action@v3
      with:
        registry: ${{ env.DOCKER_REGISTRY }}
        username: ${{ github.actor }}
        password: ${{ secrets.GITHUB_TOKEN }}
        
    - name: 📋 Extract metadata
      id: meta
      uses: docker/metadata-action@v5
      with:
        images: ${{ env.DOCKER_REGISTRY }}/${{ github.repository }}/${{ env.IMAGE_NAME }}
        tags: |
          type=ref,event=branch
          type=sha,prefix=staging-
          type=raw,value=staging-latest
          type=raw,value=${{ needs.pre-deployment.outputs.version }}
          
    - name: 🏗️ Build and push Docker image
      id: build
      uses: docker/build-push-action@v5
      with:
        context: .
        platforms: linux/amd64
        push: true
        tags: ${{ steps.meta.outputs.tags }}
        labels: ${{ steps.meta.outputs.labels }}
        cache-from: type=gha
        cache-to: type=gha,mode=max
        build-args: |
          ENVIRONMENT=staging
          VERSION=${{ needs.pre-deployment.outputs.version }}
          
    - name: 🔍 Scan image for vulnerabilities
      uses: aquasecurity/trivy-action@master
      with:
        image-ref: ${{ steps.meta.outputs.tags }}
        format: 'sarif'
        output: 'trivy-staging-results.sarif'
        
    - name: 📊 Upload Trivy scan results
      uses: github/codeql-action/upload-sarif@v2
      if: always()
      with:
        sarif_file: 'trivy-staging-results.sarif'

  # Job 3: AWS Infrastructure Preparation
  aws-infrastructure:
    name: 🏗️ AWS Infrastructure Setup
    runs-on: ubuntu-latest
    timeout-minutes: 20
    needs: [pre-deployment, build-and-push]
    outputs:
      ec2_instance_id: ${{ steps.infrastructure.outputs.ec2_instance_id }}
      rds_endpoint: ${{ steps.infrastructure.outputs.rds_endpoint }}
      
    steps:
    - name: 📥 Checkout code
      uses: actions/checkout@v4
      
    - name: 🔧 Configure AWS credentials
      uses: aws-actions/configure-aws-credentials@v4
      with:
        aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
        aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
        aws-region: ${{ env.AWS_DEFAULT_REGION }}
        
    - name: 🏗️ Setup AWS infrastructure
      id: infrastructure
      run: |
        echo "🏗️ Setting up AWS infrastructure for staging..."
        
        # Check if EC2 instance exists
        INSTANCE_ID=$(aws ec2 describe-instances \
          --filters "Name=tag:Environment,Values=staging" "Name=tag:Application,Values=fluxtrader" "Name=instance-state-name,Values=running" \
          --query "Reservations[0].Instances[0].InstanceId" \
          --output text 2>/dev/null || echo "None")
        
        if [[ "$INSTANCE_ID" == "None" || "$INSTANCE_ID" == "null" ]]; then
          echo "🚀 Creating new EC2 instance for staging..."
          
          # Create security group if it doesn't exist
          SG_ID=$(aws ec2 describe-security-groups \
            --filters "Name=group-name,Values=fluxtrader-staging-sg" \
            --query "SecurityGroups[0].GroupId" \
            --output text 2>/dev/null || echo "None")
          
          if [[ "$SG_ID" == "None" || "$SG_ID" == "null" ]]; then
            echo "🔒 Creating security group..."
            SG_ID=$(aws ec2 create-security-group \
              --group-name fluxtrader-staging-sg \
              --description "FluxTrader Staging Security Group" \
              --query "GroupId" \
              --output text)
            
            # Add rules for HTTP, HTTPS, SSH, and application port
            aws ec2 authorize-security-group-ingress \
              --group-id $SG_ID \
              --protocol tcp \
              --port 22 \
              --cidr 0.0.0.0/0
            
            aws ec2 authorize-security-group-ingress \
              --group-id $SG_ID \
              --protocol tcp \
              --port 80 \
              --cidr 0.0.0.0/0
            
            aws ec2 authorize-security-group-ingress \
              --group-id $SG_ID \
              --protocol tcp \
              --port 443 \
              --cidr 0.0.0.0/0
            
            aws ec2 authorize-security-group-ingress \
              --group-id $SG_ID \
              --protocol tcp \
              --port 8000 \
              --cidr 0.0.0.0/0
          fi
          
          # Launch EC2 instance
          INSTANCE_ID=$(aws ec2 run-instances \
            --image-id ami-0c02fb55956c7d316 \
            --instance-type ${{ env.EC2_INSTANCE_TYPE }} \
            --key-name ${{ secrets.AWS_KEY_PAIR_NAME }} \
            --security-group-ids $SG_ID \
            --tag-specifications "ResourceType=instance,Tags=[{Key=Name,Value=fluxtrader-staging},{Key=Environment,Value=staging},{Key=Application,Value=fluxtrader}]" \
            --user-data file://scripts/user-data-staging.sh \
            --query "Instances[0].InstanceId" \
            --output text)
          
          echo "⏳ Waiting for instance to be running..."
          aws ec2 wait instance-running --instance-ids $INSTANCE_ID
        fi
        
        echo "ec2_instance_id=$INSTANCE_ID" >> $GITHUB_OUTPUT
        echo "✅ EC2 Instance ID: $INSTANCE_ID"
        
        # Check RDS instance
        RDS_ENDPOINT=$(aws rds describe-db-instances \
          --db-instance-identifier fluxtrader-staging \
          --query "DBInstances[0].Endpoint.Address" \
          --output text 2>/dev/null || echo "None")
        
        if [[ "$RDS_ENDPOINT" == "None" || "$RDS_ENDPOINT" == "null" ]]; then
          echo "🗄️ Creating RDS instance for staging..."
          
          aws rds create-db-instance \
            --db-instance-identifier fluxtrader-staging \
            --db-instance-class ${{ env.RDS_INSTANCE_CLASS }} \
            --engine postgres \
            --master-username fluxtrader \
            --master-user-password ${{ secrets.RDS_MASTER_PASSWORD }} \
            --allocated-storage 20 \
            --vpc-security-group-ids $SG_ID \
            --db-name kamikaze \
            --backup-retention-period 7 \
            --storage-encrypted \
            --tags Key=Environment,Value=staging Key=Application,Value=fluxtrader
          
          echo "⏳ Waiting for RDS instance to be available..."
          aws rds wait db-instance-available --db-instance-identifier fluxtrader-staging
          
          RDS_ENDPOINT=$(aws rds describe-db-instances \
            --db-instance-identifier fluxtrader-staging \
            --query "DBInstances[0].Endpoint.Address" \
            --output text)
        fi
        
        echo "rds_endpoint=$RDS_ENDPOINT" >> $GITHUB_OUTPUT
        echo "✅ RDS Endpoint: $RDS_ENDPOINT"

  # Job 4: Deploy to AWS Staging
  deploy-staging:
    name: 🚀 Deploy to AWS Staging
    runs-on: ubuntu-latest
    timeout-minutes: 25
    needs: [pre-deployment, build-and-push, aws-infrastructure]
    environment:
      name: staging
      url: http://${{ needs.aws-infrastructure.outputs.ec2_instance_id }}.compute-1.amazonaws.com:8000

    steps:
    - name: 📥 Checkout code
      uses: actions/checkout@v4

    - name: 🔧 Configure AWS credentials
      uses: aws-actions/configure-aws-credentials@v4
      with:
        aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
        aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
        aws-region: ${{ env.AWS_DEFAULT_REGION }}

    - name: 🔐 Update AWS Secrets Manager
      run: |
        echo "🔐 Updating secrets in AWS Secrets Manager..."

        # Update database credentials
        aws secretsmanager put-secret-value \
          --secret-id "fluxtrader/staging/database/main" \
          --secret-string '{
            "host": "${{ needs.aws-infrastructure.outputs.rds_endpoint }}",
            "port": "5432",
            "database": "kamikaze",
            "username": "fluxtrader",
            "password": "${{ secrets.RDS_MASTER_PASSWORD }}",
            "ssl_mode": "require",
            "min_size": "5",
            "max_size": "20",
            "timeout": "60"
          }' || aws secretsmanager create-secret \
          --name "fluxtrader/staging/database/main" \
          --description "FluxTrader Staging Database Credentials" \
          --secret-string '{
            "host": "${{ needs.aws-infrastructure.outputs.rds_endpoint }}",
            "port": "5432",
            "database": "kamikaze",
            "username": "fluxtrader",
            "password": "${{ secrets.RDS_MASTER_PASSWORD }}",
            "ssl_mode": "require",
            "min_size": "5",
            "max_size": "20",
            "timeout": "60"
          }'

        # Update trading API keys
        aws secretsmanager put-secret-value \
          --secret-id "fluxtrader/staging/trading/api-keys" \
          --secret-string '{
            "binance_api_key": "${{ secrets.BINANCE_API_KEY_STAGING }}",
            "binance_secret_key": "${{ secrets.BINANCE_SECRET_KEY_STAGING }}",
            "binance_testnet": true,
            "groq_api_key": "${{ secrets.GROQ_API_KEY }}"
          }' || aws secretsmanager create-secret \
          --name "fluxtrader/staging/trading/api-keys" \
          --description "FluxTrader Staging Trading API Keys" \
          --secret-string '{
            "binance_api_key": "${{ secrets.BINANCE_API_KEY_STAGING }}",
            "binance_secret_key": "${{ secrets.BINANCE_SECRET_KEY_STAGING }}",
            "binance_testnet": true,
            "groq_api_key": "${{ secrets.GROQ_API_KEY }}"
          }'

        # Update application secrets
        aws secretsmanager put-secret-value \
          --secret-id "fluxtrader/staging/application/secrets" \
          --secret-string '{
            "jwt_secret": "${{ secrets.JWT_SECRET_STAGING }}",
            "encryption_key": "${{ secrets.ENCRYPTION_KEY_STAGING }}",
            "credentials_encryption_key": "${{ secrets.CREDENTIALS_ENCRYPTION_KEY_STAGING }}"
          }' || aws secretsmanager create-secret \
          --name "fluxtrader/staging/application/secrets" \
          --description "FluxTrader Staging Application Secrets" \
          --secret-string '{
            "jwt_secret": "${{ secrets.JWT_SECRET_STAGING }}",
            "encryption_key": "${{ secrets.ENCRYPTION_KEY_STAGING }}",
            "credentials_encryption_key": "${{ secrets.CREDENTIALS_ENCRYPTION_KEY_STAGING }}"
          }'

        echo "✅ Secrets updated in AWS Secrets Manager"

    - name: 🚀 Deploy to EC2 instance
      run: |
        echo "🚀 Deploying FluxTrader to staging EC2 instance..."

        INSTANCE_ID="${{ needs.aws-infrastructure.outputs.ec2_instance_id }}"
        IMAGE_TAG="${{ needs.build-and-push.outputs.image_tag }}"

        # Get instance public IP
        PUBLIC_IP=$(aws ec2 describe-instances \
          --instance-ids $INSTANCE_ID \
          --query "Reservations[0].Instances[0].PublicIpAddress" \
          --output text)

        echo "📍 Instance IP: $PUBLIC_IP"
        echo "📦 Image: $IMAGE_TAG"

        # Create deployment script
        cat > deploy-staging.sh << 'EOF'
        #!/bin/bash
        set -e

        echo "🔄 Stopping existing containers..."
        sudo docker stop fluxtrader-staging || true
        sudo docker rm fluxtrader-staging || true

        echo "📥 Pulling latest image..."
        sudo docker pull $1

        echo "🚀 Starting new container..."
        sudo docker run -d \
          --name fluxtrader-staging \
          --restart unless-stopped \
          -p 8000:8000 \
          -e ENVIRONMENT=staging \
          -e AWS_DEFAULT_REGION=$2 \
          -e AWS_ACCESS_KEY_ID=$3 \
          -e AWS_SECRET_ACCESS_KEY=$4 \
          $1

        echo "✅ Deployment completed"
        EOF

        # Copy and execute deployment script
        aws ssm send-command \
          --instance-ids $INSTANCE_ID \
          --document-name "AWS-RunShellScript" \
          --parameters "commands=[
            'curl -fsSL https://get.docker.com -o get-docker.sh',
            'sudo sh get-docker.sh',
            'sudo usermod -aG docker ec2-user',
            'sudo systemctl start docker',
            'sudo systemctl enable docker',
            'echo \"$IMAGE_TAG\" > /tmp/image_tag',
            'sudo docker pull $IMAGE_TAG',
            'sudo docker stop fluxtrader-staging || true',
            'sudo docker rm fluxtrader-staging || true',
            'sudo docker run -d --name fluxtrader-staging --restart unless-stopped -p 8000:8000 -e ENVIRONMENT=staging -e AWS_DEFAULT_REGION=${{ env.AWS_DEFAULT_REGION }} $IMAGE_TAG'
          ]" \
          --output text

        echo "✅ Deployment commands sent to EC2 instance"

    - name: ⏳ Wait for deployment
      run: |
        echo "⏳ Waiting for application to start..."
        sleep 60

    - name: 🔍 Health checks
      run: |
        echo "🔍 Performing health checks..."

        INSTANCE_ID="${{ needs.aws-infrastructure.outputs.ec2_instance_id }}"
        PUBLIC_IP=$(aws ec2 describe-instances \
          --instance-ids $INSTANCE_ID \
          --query "Reservations[0].Instances[0].PublicIpAddress" \
          --output text)

        # Health check with retries
        for i in {1..10}; do
          echo "🔍 Health check attempt $i/10..."

          if curl -f "http://$PUBLIC_IP:8000/health" --connect-timeout 10 --max-time 30; then
            echo "✅ Health check passed"
            break
          else
            echo "⏳ Health check failed, retrying in 30 seconds..."
            sleep 30
          fi

          if [ $i -eq 10 ]; then
            echo "❌ Health checks failed after 10 attempts"
            exit 1
          fi
        done

    - name: 🧪 Smoke tests
      run: |
        echo "🧪 Running smoke tests..."

        INSTANCE_ID="${{ needs.aws-infrastructure.outputs.ec2_instance_id }}"
        PUBLIC_IP=$(aws ec2 describe-instances \
          --instance-ids $INSTANCE_ID \
          --query "Reservations[0].Instances[0].PublicIpAddress" \
          --output text)

        BASE_URL="http://$PUBLIC_IP:8000"

        # Test API endpoints
        curl -f "$BASE_URL/health" || exit 1
        curl -f "$BASE_URL/docs" || exit 1

        echo "✅ Smoke tests passed"

    - name: 📢 Deployment success notification
      if: success()
      run: |
        INSTANCE_ID="${{ needs.aws-infrastructure.outputs.ec2_instance_id }}"
        PUBLIC_IP=$(aws ec2 describe-instances \
          --instance-ids $INSTANCE_ID \
          --query "Reservations[0].Instances[0].PublicIpAddress" \
          --output text)

        echo "## 🚀 AWS Staging Deployment Successful" >> $GITHUB_STEP_SUMMARY
        echo "" >> $GITHUB_STEP_SUMMARY
        echo "- **Version**: ${{ needs.pre-deployment.outputs.version }}" >> $GITHUB_STEP_SUMMARY
        echo "- **Image**: ${{ needs.build-and-push.outputs.image_tag }}" >> $GITHUB_STEP_SUMMARY
        echo "- **EC2 Instance**: $INSTANCE_ID" >> $GITHUB_STEP_SUMMARY
        echo "- **Public URL**: [http://$PUBLIC_IP:8000](http://$PUBLIC_IP:8000)" >> $GITHUB_STEP_SUMMARY
        echo "- **RDS Endpoint**: ${{ needs.aws-infrastructure.outputs.rds_endpoint }}" >> $GITHUB_STEP_SUMMARY
        echo "- **Deployed at**: $(date -u)" >> $GITHUB_STEP_SUMMARY
        echo "" >> $GITHUB_STEP_SUMMARY
        echo "### 🔐 Security Features" >> $GITHUB_STEP_SUMMARY
        echo "- ✅ AWS Secrets Manager integration" >> $GITHUB_STEP_SUMMARY
        echo "- ✅ Encrypted RDS storage" >> $GITHUB_STEP_SUMMARY
        echo "- ✅ Security group restrictions" >> $GITHUB_STEP_SUMMARY
        echo "- ✅ Container vulnerability scanning" >> $GITHUB_STEP_SUMMARY

  # Job 5: Post-deployment monitoring
  post-deployment:
    name: 📊 Post-deployment Monitoring
    runs-on: ubuntu-latest
    timeout-minutes: 15
    needs: [aws-infrastructure, deploy-staging]
    if: success()

    steps:
    - name: 🔧 Configure AWS credentials
      uses: aws-actions/configure-aws-credentials@v4
      with:
        aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
        aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
        aws-region: ${{ env.AWS_DEFAULT_REGION }}

    - name: 📊 Monitor deployment
      run: |
        echo "📊 Monitoring staging deployment..."

        INSTANCE_ID="${{ needs.aws-infrastructure.outputs.ec2_instance_id }}"
        PUBLIC_IP=$(aws ec2 describe-instances \
          --instance-ids $INSTANCE_ID \
          --query "Reservations[0].Instances[0].PublicIpAddress" \
          --output text)

        # Monitor for 10 minutes
        for i in {1..10}; do
          echo "🔍 Monitoring check $i/10..."

          # Check instance health
          INSTANCE_STATE=$(aws ec2 describe-instances \
            --instance-ids $INSTANCE_ID \
            --query "Reservations[0].Instances[0].State.Name" \
            --output text)

          echo "📊 Instance state: $INSTANCE_STATE"

          # Check application health
          if curl -f "http://$PUBLIC_IP:8000/health" --connect-timeout 5 --max-time 10; then
            echo "✅ Application healthy"
          else
            echo "⚠️ Application health check failed"
          fi

          sleep 60
        done

        echo "✅ Post-deployment monitoring completed"

  # Job 6: Rollback capability
  rollback:
    name: 🔄 Rollback (On Failure)
    runs-on: ubuntu-latest
    timeout-minutes: 15
    needs: [aws-infrastructure, deploy-staging]
    if: failure()

    steps:
    - name: 🔧 Configure AWS credentials
      uses: aws-actions/configure-aws-credentials@v4
      with:
        aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
        aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
        aws-region: ${{ env.AWS_DEFAULT_REGION }}

    - name: 🔄 Execute rollback
      run: |
        echo "🔄 Executing rollback for staging deployment..."

        INSTANCE_ID="${{ needs.aws-infrastructure.outputs.ec2_instance_id }}"

        # Get previous image tag (implement your rollback logic here)
        PREVIOUS_IMAGE="ghcr.io/${{ github.repository }}/fluxtrader:staging-previous"

        # Rollback deployment
        aws ssm send-command \
          --instance-ids $INSTANCE_ID \
          --document-name "AWS-RunShellScript" \
          --parameters "commands=[
            'sudo docker stop fluxtrader-staging || true',
            'sudo docker rm fluxtrader-staging || true',
            'sudo docker run -d --name fluxtrader-staging --restart unless-stopped -p 8000:8000 -e ENVIRONMENT=staging $PREVIOUS_IMAGE'
          ]" \
          --output text

        echo "✅ Rollback completed"

    - name: 📢 Rollback notification
      run: |
        echo "## 🔄 AWS Staging Rollback Executed" >> $GITHUB_STEP_SUMMARY
        echo "" >> $GITHUB_STEP_SUMMARY
        echo "- **Rollback executed at**: $(date -u)" >> $GITHUB_STEP_SUMMARY
        echo "- **Reason**: Deployment failure detected" >> $GITHUB_STEP_SUMMARY
        echo "- **Instance**: ${{ needs.aws-infrastructure.outputs.ec2_instance_id }}" >> $GITHUB_STEP_SUMMARY
        echo "- **Status**: System restored to previous stable state" >> $GITHUB_STEP_SUMMARY
